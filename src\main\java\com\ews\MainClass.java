package com.ews;

public class MainClass
{
  public static void main(String[] args)
  {
    HttpClient httpClient = new HttpClient("hgmt5.everwings.online"); 
    httpClient.sendAuth("2003", "Y_D2PgFm", "484", "test") ;     
    try
    {
      Thread.sleep(1000); 
    }
    catch(Exception ex)
    {
      //System.out.println(ex.toString());
    }
    
    //{ "retcode" : "0 Done", "answer" :[]}
    //查询订单  -ok
    //System.out.println(httpClient.getHistoryOrder("99010221","1732293122","1742293122"));
    //根据組查账号信息  -ok
   //System.out.println(httpClient.getUserInfoByGroup("FAT\\MM\\STP20"));
    //httpClient.getTradeInfoByGroup();
    //根据账号查交易状态
   //System.out.println(httpClient.getTradeInfoByLoginid("89930104"));
    //测试出入金  -ok
    //org.json.JSONObject json=httpClient.balanceCharge("49001","2","-200","api");
    //System.out.println(json.toString());
     //System.out.println(json.getJSONObject("answer").getString("ticket"));
    //获取交易品种-ok
   // System.out.println(httpClient.getProds());
    //获取交易组总数-ok
     //System.out.println(httpClient.getGroupTotal());
    //根据索引获取交易组信息-ok
   // for(int i=0;i<41;i++)
    //System.out.println(httpClient.getGroupInfoByIndex(i));
    //创建交易账号-ok
     //System.out.println(httpClient.addNewLogin("99000000","ABab12!!","ABab12!!1","FAT\\MM\\W1","fuyu","100","<EMAIL>","CHN"));
    //更改交易账号的杠杆-ok
    //System.out.println(httpClient.updateLoginLeverage("60000006","200"));
    //更改交易账号密码-ok
    //httpClient.updateLoginPwd("8000567","1Ar#pqkj");

  }
}
