<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
      <title>[[${CRM_TITLE}]]</title>
    <!-- CSS files -->
    <link href="../dist/css/tabler.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/tabler-flags.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/tabler-payments.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/tabler-vendors.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/demo.min.css?1684106062" rel="stylesheet"/>
	 <script src="js/jquery.min.js"></script>
      <script src="js/layer/layer.js"></script>
<link rel="shortcut icon" th:href="${CRM_LOGO2}">
<script async th:src="@{'https://www.googletagmanager.com/gtag/js?id='+${JS_ID1}}" th:if="${HEADER_SCRIPT_STATUS}==1"></script>
<script  th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1' ">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', [[${JS_ID1}]]);
</script>

<script  th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', [[${JS_ID2}]]);
fbq('track', 'PageView');
</script>
<noscript>
<img th:if="${HEADER_SCRIPT_STATUS} eq '1'"  height="1" width="1" style="display:none"
th:src="@{'https://www.facebook.com/tr?id='+${JS_ID2}+'&ev=PageView&noscript=1'}"/>
</noscript>

<!-- Google Tag Manager -->
<script th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer',[[${JS_ID3}]]);</script>
<!-- End Google Tag Manager -->
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
     <style>
      @media screen and (min-width: 1080px) {
      .lest {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
      }

      .conter {
        padding: 0px 20px;
        width: 85vw;
        background-color: #000000;
        opacity: 0.66;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content:space-between;
        align-items: center;
      }

      .second {
        display: flex;
        margin-right: 10px;
      }

      .button {
        border: 1px solid #fff;
        border-radius: 5px;
        margin-right: 10px;
        padding: 0px 15px;
        display: flex;
        align-items: center;
      }

      .button a {
        color: #fff;
        font-size: 10px;
      }

      .button img{
        width: 30px;
      }

      .button .winimg{
        width: 15px;
        padding-right: 5px;
      }
    }

    @media screen and (max-width: 1080px) {
      .lest {
        width: 100%;
        display: flex;
        justify-content: center;
        /* position: fixed;
        bottom: 0;
        left: 0;
        right: 0; */
      }

      .conter {
        padding: 0px 20px;
        width: 80%;
        background-color: #000000;
        opacity: 0.66;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .second {
        width: 100%;
        padding: 10px 0px;
      }

      .button {
        border: 1px solid #fff;
        border-radius: 5px;
        margin-right: 10px;
        padding: 0px 20px;
      }

      .button a {
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .button img{
        width: 30px;
      }

      .button .winimg{
        width: 15px;
        padding-right: 5px;
      }
    }
    </style>
  </head>
  <body >
    <script src="./dist/js/demo-theme.min.js?1684106062"></script>
    <div class="page">
      <!-- Navbar -->
      <header class="navbar navbar-expand-md d-print-none" >
        <div class="container-xl" >
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="main">
              <img th:src="${CRM_LOGO1}" width="110" height="32" alt="Tabler" class="navbar-brand-image">
            </a>
          </h1>
		   <div class="collapse navbar-collapse" id="navbar-menu" >
          <div  >
            <div class="container-xl" >
              <ul class="navbar-nav"  >
                <li class="nav-item ">
                  <a class="nav-link" href="main" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/home -->
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
                    </span>
                    <span class="nav-link-title"  th:text="#{menu.home}">
                      Home
                    </span>
                  </a>
                </li>
                <li class="nav-item ">
                  <a class="nav-link" href="fundTransfer" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/package -->
                     <img src="../img/Funds.png" width="18" height="18"/>
                    </span>
					 <span class="nav-link-title"   th:text="#{menu.fundtransfer}">
                      Fund Transfer
                    </span>
                   
                  </a>
                  
                </li>
                <li class="nav-item " >
                  <a class="nav-link" href="fundList" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     资金记录
                    </span>
                  </a>
                </li>
                <li class="nav-item active" >
                  <a class="nav-link" href="orderList"  >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     交易记录
                    </span>
                  </a>
                </li>
                
                   <li class="nav-item" th:if="${WEB_TRADER_STATUS}  eq '1' ">
                  <a class="nav-link" th:href="${WEB_TRADER_URL}" target="_blank" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     WebTrader
                    </span>
                  </a>
                </li>
               
				 <li class="nav-item">
                  <a class="nav-link" href="profile" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/star -->
                    <img src="../img/Profile.png" width="18" height="18"/>
                    </span>
                  <span class="nav-link-title"   th:text="#{menu.profile}">
                      Profile 
                    </span>
                  </a>
                </li>
                 <li class="nav-item" th:if="${SUPPORT_STATUS}  eq '1'">
                  <a class="nav-link" th:href="${SupportCenter}" target="_blank" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                     <img src="../img/CustomerService.png" width="18" height="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.support}">
                     Support
                    </span>
                  </a>
                </li>
                
                <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="changeLanguage">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/Langauge.png" width="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.language}">
                     Preferred Language
                    </span>
                  </a>
                </li>
                
                 <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="modifyPwd">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/ChangePassword.png" width="18"/>
                    </span>
                    <span class="nav-link-title"  th:text="#{menu.modifypassword}">
                     Modify Password
                    </span>
                  </a>
                </li>
             
               <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="logout">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/Logout.png" width="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.logout}">
                    Logout
                    </span>
                  </a>
                </li>
              
               
               
               
              </ul>
            
            </div>
          </div>
        </div>
        <div class="navbar-nav flex-row order-md-last">
          
          
             <div class="nav-item d-none d-md-flex me-3">
              <div class="btn-list">
                 [[${userInfo.userName}]]
              </div>
            </div>
            
            <div class="nav-item dropdown">
              <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
              
                <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"><img src="../img/Langauge.png" width="20"/></div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
              <a href="javascript:updateaLang('zh-cn');" class="dropdown-item"  >简体中文</a>
              <a href="javascript:updateaLang('zh-tw');" class="dropdown-item"  >繁體中文</a>
              <a href="javascript:updateaLang('en');" class="dropdown-item"  >English</a>
              <a href="javascript:updateaLang('th');" class="dropdown-item"  >ภาษาไทย</a>
              <a href="javascript:updateaLang('ms');" class="dropdown-item"  >Bahasa Malay</a>
              <a href="javascript:updateaLang('id');" class="dropdown-item"  >Bahasa Indonesia</a>
              <a href="javascript:updateaLang('vi');" class="dropdown-item" >Tiếng Việt</a>
               <a href="javascript:updateaLang('ja');" class="dropdown-item" >日本語</a>
              </div>
            </div>
            <div class="nav-item d-none d-md-flex me-3">
             <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"> <a href="modifyPwd" ><img src="../img/ChangePassword.png" width="22"/></a></div>
                </div>
            </div>
             <div class="nav-item d-none d-md-flex me-3">
               <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"> <a href="logout" ><img src="../img/Logout.png" width="20"/></a></div>
                </div>
            </div>
            
            
            
          </div>
        </div>
      </header>

      <div class="page-wrapper">
        <!-- Page header -->
        <div class="page-header d-print-none">
          <div class="container-xl">
            <div class="row g-2 align-items-center">
              <div class="col">
                <!-- Page pre-title -->
                 <div class="page-pretitle"   >
                  交易记录
                </div>
				 <h2 class="page-title"   >
                  历史订单
                </h2>
                
               
              </div>
              
            </div>
          </div>
        </div>
      
        <!-- Page body -->
        <div class="page-body">
          <div class="container-xl">
            <div class="row row-deck row-cards">
             
              
            
             <center>
              <div class="col-lg-9">
                <div class="card">
                  <div class="card-body">
                     <!-- 日期筛选表单 -->
                 <center>
                  <div class="row g-2 mt-3">
                    <div class="col-12">
                      <form id="dateFilterForm" method="post" action="orderList">
                        <div class="row g-2 align-items-end">
                          <div class="col-md-3">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" name="startDate" id="startDate" 
                                   th:value="${startDate}" placeholder="选择开始日期">
                          </div>
                          <div class="col-md-3">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" name="endDate" id="endDate" 
                                   th:value="${endDate}" placeholder="选择结束日期">
                          </div>
                          <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                              <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <circle cx="10" cy="10" r="7"/>
                                <path d="m21 21-6-6"/>
                              </svg>
                              查询
                            </button>
                          </div>
                          <div class="col-md-2">
                            <button type="button" class="btn btn-secondary w-100" onclick="clearDateFilter()">
                              <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M4 7h16"/>
                                <path d="M10 11v6"/>
                                <path d="M14 11v6"/>
                                <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/>
                                <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/>
                              </svg>
                              清空
                            </button>
                          </div>
                         
                          
                        </div>
                      </form>
                    </div>
                  </div>
                </center>
                <br/>
                  
                   <table
		class="table table-vcenter card-table" id="orderTable">
                      <thead>
                        <tr>
                          <th  >订单号</th>
                          <th  >交易账号</th>
                          <th th:if="${isMobile==0}" >平仓时间</th>
                          <th  th:if="${isMobile==0}" >平仓金额</th>
                          <th >交易类型</th>
                          <th >手数</th>
                          <th >盈亏</th>
                       </tr>

                      </thead>
                      <tbody id="orderTableBody">
                        <tr th:each="column,columnStat:${orderList}" class="order-row">
                        
                            <td >[[${column.orderNo}]]</td>
                            <td >[[${column.loginId}]]</td>
                            <td th:if="${isMobile==0}" th:text="${#dates.format(new java.util.Date(column.closeTime * 1000L), 'yyyy-MM-dd HH:mm:ss')}"></td>
                            <td th:if="${isMobile==0}" >[[${column.closePrice}]]</td>
                            <td th:if="${column.type==0}"><font color="blue" >BUY</font></td>
                            <td th:if="${column.type==1}"><font color="green">SELL</font></td>
                            <td >[[${column.volume}]]</td>
                            <td >[[${column.profit}]]</td>
                          </tr>
                        </tr>
                      </tbody>
                    </table>
                    
                    <!-- 分页导航 -->
                    <div class="row mt-3">
                      <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                          <div class="text-muted small">
                            显示第 <span id="startRecord">1</span> - <span id="endRecord">10</span> 条，共 <span id="totalRecords">0</span> 条记录  &nbsp;&nbsp; 交易手数：[[${yjss}]]  盈亏：[[${hlje}]] 
                          </div>
                          <nav aria-label="页面导航">
                            <ul class="pagination mb-0" id="pagination">
                              <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                          </nav>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
			  </center>
             
            </div>
          </div>
        </div>
      
         <footer class="footer footer-transparent d-print-none">
          <div class="container-xl">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
				<center>
                  <li class="list-inline-item">
                   ©Copyright 2025  All Rights Reserved. v[[${CRM_VERSION}]] - Licensed to [[${CRM_LICENSED}]]
                  </li>
				  </center>
                 
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <br/><br/>
        <div class="lest">
          <div class="conter">
            <div class="second" th:if="${isMobile==0}">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">Desktop MT5</div>
              <div class="button" style="margin-top:5px;"><img class="winimg" src="../img/win.png" alt=""><a th:href="${APP_URL1}">Windows</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a th:href="${APP_URL2}">MacOS</a></div>
            </div>
            <div class="second">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">Mobile MT5</div>
              <div class="button" style="margin-top:5px;"><img src="../img/google.png" alt=""><a  th:href="${APP_URL3}"  >Google</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a  th:href="${APP_URL4}">IOS</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/apk.png" alt=""><a  th:href="${APP_URL5}">APK</a></div>
            </div>
            <div class="second" th:if="${APP3_STATUS eq '1'}">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">70 Knows Mobile</div>
              <div class="button"   style="margin-top:5px;"><img src="../img/google.png" alt=""><a  th:href="${APP_URL6}" >Google</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a  th:href="${APP_URL7}">IOS</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/apk.png" alt=""><a  th:href="${APP_URL8}">APK</a></div>
            </div>
          </div>
        </div>
    <form class="card card-md" id="languageForm"  method="post">
                <input type="hidden" name="planguage" id="planguage"/>
        </form>
    <!-- Libs JS -->
    <script src="../dist/libs/apexcharts/dist/apexcharts.min.js?1684106062" defer></script>
    <script src="../dist/libs/jsvectormap/dist/js/jsvectormap.min.js?1684106062" defer></script>
    <script src="../dist/libs/jsvectormap/dist/maps/world.js?1684106062" defer></script>
    <script src="../dist/libs/jsvectormap/dist/maps/world-merc.js?1684106062" defer></script>
    <!-- Tabler Core -->
    <script src="../dist/js/tabler.min.js?1684106062" defer></script>
    <script src="../dist/js/demo.min.js?1684106062" defer></script>
   
    	<script  th:inline="javascript">

function updateaLang(obj){

	    document.getElementById("planguage").value=obj;
		$.ajax({
			//几个参数需要注意一下
			type: "POST",//方法类型
			url: "saveLanguage.do" ,//url
			data: $('#languageForm').serialize(),
			success: function (result) {
				
				location.reload();
			},
			error : function() {
			
			}
		});
	
	
}
function toAlert(){
	layer.open({
		style : 'margin-bottom : 70%;',
		content : '[[#{ext.prompt1}]]',
		skin : 'msg',
		time : 5000,btn:['OK']
	   ,title: 'INFO'
	});
	
};

function clearDateFilter(){
	document.getElementById("startDate").value = "";
	document.getElementById("endDate").value = "";
	// 提交表单以清空筛选条件
	document.getElementById("dateFilterForm").submit();
}

// 分页相关变量
let currentPage = 1;
const recordsPerPage = 10;
let totalRecords = 0;
let allRows = [];

// 页面加载完成后初始化分页
document.addEventListener('DOMContentLoaded', function() {
    initializePagination();
});

// 在日期筛选表单提交后重新初始化分页
document.getElementById('dateFilterForm').addEventListener('submit', function() {
    // 延迟初始化分页，等待页面重新加载
    setTimeout(function() {
        initializePagination();
    }, 100);
});

function initializePagination() {
    // 获取所有数据行
    allRows = Array.from(document.querySelectorAll('.order-row'));
    totalRecords = allRows.length;
    
    // 更新记录总数显示
    document.getElementById('totalRecords').textContent = totalRecords;
    
    // 如果有数据，显示第一页
    if (totalRecords > 0) {
        showPage(1);
        generatePagination();
    } else {
        // 没有数据时的显示
        document.getElementById('startRecord').textContent = '0';
        document.getElementById('endRecord').textContent = '0';
        document.getElementById('pagination').innerHTML = '';
    }
}

function showPage(page) {
    currentPage = page;
    
    // 计算开始和结束索引
    const startIndex = (page - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    
    // 隐藏所有行
    allRows.forEach(row => {
        row.style.display = 'none';
    });
    
    // 显示当前页的行
    for (let i = startIndex; i < endIndex && i < allRows.length; i++) {
        allRows[i].style.display = '';
    }
    
    // 更新记录显示信息
    const startRecord = totalRecords > 0 ? startIndex + 1 : 0;
    const endRecord = Math.min(endIndex, totalRecords);
    
    document.getElementById('startRecord').textContent = startRecord;
    document.getElementById('endRecord').textContent = endRecord;
    
    // 更新分页按钮状态
    generatePagination();
}

function generatePagination() {
    const totalPages = Math.ceil(totalRecords / recordsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页按钮
    if (currentPage > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="javascript:void(0)" onclick="showPage(${currentPage - 1})">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <polyline points="15,18 9,12 15,6"/>
                    </svg>
                    上一页
                </a>
            </li>
        `;
    } else {
        paginationHTML += `
            <li class="page-item disabled">
                <span class="page-link">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <polyline points="15,18 9,12 15,6"/>
                    </svg>
                    上一页
                </span>
            </li>
        `;
    }
    
    // 页码按钮
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);
    
    // 如果当前页接近开始，显示更多后面的页码
    if (currentPage <= 3) {
        endPage = Math.min(totalPages, 5);
    }
    
    // 如果当前页接近结束，显示更多前面的页码
    if (currentPage >= totalPages - 2) {
        startPage = Math.max(1, totalPages - 4);
    }
    
    // 如果不是从第1页开始，添加第1页和省略号
    if (startPage > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="javascript:void(0)" onclick="showPage(1)">1</a>
            </li>
        `;
        if (startPage > 2) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            paginationHTML += `
                <li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>
            `;
        } else {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="javascript:void(0)" onclick="showPage(${i})">${i}</a>
                </li>
            `;
        }
    }
    
    // 如果不是到最后一页，添加省略号和最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="javascript:void(0)" onclick="showPage(${totalPages})">${totalPages}</a>
            </li>
        `;
    }
    
    // 下一页按钮
    if (currentPage < totalPages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="javascript:void(0)" onclick="showPage(${currentPage + 1})">
                    下一页
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <polyline points="9,18 15,12 9,6"/>
                    </svg>
                </a>
            </li>
        `;
    } else {
        paginationHTML += `
            <li class="page-item disabled">
                <span class="page-link">
                    下一页
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <polyline points="9,18 15,12 9,6"/>
                    </svg>
                </span>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHTML;
}
</script>	
<script src="https://cdn.customgpt.ai/js/chat.js"  th:inline="javascript" th:if="${FOOTER2_SCRIPT_STATUS} eq '1'"></script>
<script th:if="${FOOTER2_SCRIPT_STATUS} eq '1'" th:inline="javascript" >window.onload = function () { CustomGPT.init({p_id: [[${JS2_ID}]], p_key: [[${JS2_KEY}]], reset_conversation: "1"}); };</script>
 
<!-- Start of seventyinvestech Zendesk Widget script -->
<script id="ze-snippet" th:src="@{'https://static.zdassets.com/ekr/snippet.js?key='+${JS_KEY}}"  th:if="${FOOTER_SCRIPT_STATUS} eq '1'"> </script >
<!-- End of seventyinvestech Zendesk Widget script -->
  </body>
</html>