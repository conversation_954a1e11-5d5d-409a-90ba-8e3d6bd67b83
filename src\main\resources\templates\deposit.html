<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <title>[[${CRM_TITLE}]]</title>
    <!-- CSS files -->
    <link href="../dist/css/tabler.min.css?**********" rel="stylesheet"/>
    <link href="../dist/css/tabler-flags.min.css?**********" rel="stylesheet"/>
    <link href="../dist/css/tabler-payments.min.css?**********" rel="stylesheet"/>
    <link href="../dist/css/tabler-vendors.min.css?**********" rel="stylesheet"/>
    <link href="../dist/css/demo.min.css?**********" rel="stylesheet"/>
 <script src="js/jquery.min.js"></script>
      <script src="js/layer/layer.js"></script>
<link rel="shortcut icon" th:href="${CRM_LOGO2}">
<script async th:src="@{'https://www.googletagmanager.com/gtag/js?id='+${JS_ID1}}" th:if="${HEADER_SCRIPT_STATUS}==1"></script>
<script  th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1' ">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', [[${JS_ID1}]]);
</script>

<script  th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', [[${JS_ID2}]]);
fbq('track', 'PageView');
</script>
<noscript>
<img th:if="${HEADER_SCRIPT_STATUS} eq '1'"  height="1" width="1" style="display:none"
th:src="@{'https://www.facebook.com/tr?id='+${JS_ID2}+'&ev=PageView&noscript=1'}"/>
</noscript>

<!-- Google Tag Manager -->
<script th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer',[[${JS_ID3}]]);</script>
<!-- End Google Tag Manager -->
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
      <style>
      @media screen and (min-width: 1080px) {
      .lest {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
      }

      .conter {
        padding: 0px 20px;
        width: 85vw;
        background-color: #000000;
        opacity: 0.66;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content:space-between;
        align-items: center;
      }

      .second {
        display: flex;
        margin-right: 10px;
      }

      .button {
        border: 1px solid #fff;
        border-radius: 5px;
        margin-right: 10px;
        padding: 0px 15px;
        display: flex;
        align-items: center;
      }

      .button a {
        color: #fff;
        font-size: 10px;
      }

      .button img{
        width: 30px;
      }

      .button .winimg{
        width: 15px;
        padding-right: 5px;
      }
    }

    @media screen and (max-width: 1080px) {
      .lest {
        width: 100%;
        display: flex;
        justify-content: center;
        /* position: fixed;
        bottom: 0;
        left: 0;
        right: 0; */
      }

      .conter {
        padding: 0px 20px;
        width: 80%;
        background-color: #000000;
        opacity: 0.66;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .second {
        width: 100%;
        padding: 10px 0px;
      }

      .button {
        border: 1px solid #fff;
        border-radius: 5px;
        margin-right: 10px;
        padding: 0px 20px;
      }

      .button a {
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .button img{
        width: 30px;
      }

      .button .winimg{
        width: 15px;
        padding-right: 5px;
      }
    }
    </style>
  </head>
  <body >
    <script src="./dist/js/demo-theme.min.js?**********"></script>
    <div class="page">
      <!-- Navbar -->
      <header class="navbar navbar-expand-md d-print-none" >
        <div class="container-xl" >
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="main">
              <img th:src="${CRM_LOGO1}" width="110" height="32" alt="Tabler" class="navbar-brand-image">
            </a>
          </h1>
		   <div class="collapse navbar-collapse" id="navbar-menu" >
          <div  >
            <div class="container-xl" >
              <ul class="navbar-nav"  >
                <li class="nav-item ">
                  <a class="nav-link" href="main" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/home -->
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
                    </span>
                    <span class="nav-link-title"  th:text="#{menu.home}">
                      Home
                    </span>
                  </a>
                </li>
                <li class="nav-item active">
                  <a class="nav-link" href="fundTransfer" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/package -->
                     <img src="../img/Funds.png" width="18" height="18"/>
                    </span>
					 <span class="nav-link-title"   th:text="#{menu.fundtransfer}">
                      Fund Transfer
                    </span>
                   
                  </a>
                  
                </li>
               
                <li class="nav-item" >
                  <a class="nav-link" href="fundList"   >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     资金记录
                    </span>
                  </a>
                </li>
                <li class="nav-item" >
                  <a class="nav-link" href="orderList"  >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     交易记录
                    </span>
                  </a>
                </li>


                 <li class="nav-item" th:if="${WEB_TRADER_STATUS}  eq '1' ">
                  <a class="nav-link" th:href="${WEB_TRADER_URL}" target="_blank" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     WebTrader
                    </span>
                  </a>
                </li>
				 <li class="nav-item">
                  <a class="nav-link" href="profile" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/star -->
                    <img src="../img/Profile.png" width="18" height="18"/>
                    </span>
                   <span class="nav-link-title"   th:text="#{menu.profile}">
                      Profile 
                    </span>
                  </a>
                </li>
                  <li class="nav-item" th:if="${SUPPORT_STATUS}  eq '1'">
                  <a class="nav-link" th:href="${SupportCenter}" target="_blank" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                     <img src="../img/CustomerService.png" width="18" height="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.support}">
                     Support
                    </span>
                  </a>
                </li>
                
                <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="changeLanguage">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/Langauge.png" width="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.language}">
                     Preferred Language
                    </span>
                  </a>
                </li>
                
                 <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="modifyPwd">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/ChangePassword.png" width="18"/>
                    </span>
                    <span class="nav-link-title"  th:text="#{menu.modifypassword}">
                     Modify Password
                    </span>
                  </a>
                </li>
             
               <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="logout">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/Logout.png" width="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.logout}">
                    Logout
                    </span>
                  </a>
                </li>
              
               
               
               
              </ul>
            
            </div>
          </div>
        </div>
          <div class="navbar-nav flex-row order-md-last">
          
          
             <div class="nav-item d-none d-md-flex me-3">
              <div class="btn-list">
                 [[${userInfo.userName}]]
              </div>
            </div>
            
            <div class="nav-item dropdown">
              <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
              
                <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"><img src="../img/Langauge.png" width="20"/></div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
              <a href="javascript:updateaLang('zh-cn');" class="dropdown-item"  >简体中文</a>
              <a href="javascript:updateaLang('zh-tw');" class="dropdown-item"  >繁體中文</a>
              <a href="javascript:updateaLang('en');" class="dropdown-item"  >English</a>
              <a href="javascript:updateaLang('th');" class="dropdown-item"  >ภาษาไทย</a>
              <a href="javascript:updateaLang('ms');" class="dropdown-item"  >Bahasa Malay</a>
              <a href="javascript:updateaLang('id');" class="dropdown-item"  >Bahasa Indonesia</a>
              <a href="javascript:updateaLang('vi');" class="dropdown-item" >Tiếng Việt</a>
               <a href="javascript:updateaLang('ja');" class="dropdown-item" >日本語</a>
              </div>
            </div>
            <div class="nav-item d-none d-md-flex me-3">
             <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"> <a href="modifyPwd" ><img src="../img/ChangePassword.png" width="22"/></a></div>
                </div>
            </div>
             <div class="nav-item d-none d-md-flex me-3">
               <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"> <a href="logout" ><img src="../img/Logout.png" width="20"/></a></div>
                </div>
            </div>
            
            
            
          </div>
        </div>
      </header>

      <div class="page-wrapper">
        <!-- Page header -->
        <div class="page-header d-print-none">
          <div class="container-xl">
            <div class="row g-2 align-items-center">
              <div class="col">
                <!-- Page pre-title -->
              <div class="page-pretitle"  th:text="#{deposit.title}">
                  Fund Transfer
                </div>
				 <h2 class="page-title"  th:text="#{deposit.deposit}">
                 Deposit
                </h2>
              </div>
              
            </div>
          </div>
        </div>
      
        
        
        <!-- body begin -->
        
        
         <!-- Page body -->
        <div class="page-body">
          <div class="container-xl">
            <div class="row row-deck row-cards">

          <div class="col-md-6 col-lg-9">
                <div class="row row-cards">
                  <div class="col-12">
                   
					  <div class="card">
					
              <form class="card"  id="logonForm">
                
                <div class="card-body">
                
                  <div class="mb-3">
                    <label class="form-label"  th:text="#{deposit.accountnumber}">Account Number</label>
                    <div>
                
                      <select  name="trade_id" id="trade_id" class="form-select" onChange="selectAccount();">
                         <option  th:each="column,columnStat:${tradeList}"    th:value="${column.id}">[[${column.tradeId}]]</option>
                      </select>
                    </div>
                  </div>
                
                  <div class="mb-3">
                    <label class="form-label"  th:text="#{deposit.paymentto}">Payment to</label>
                    <div>
                      <select name="bank_id" id="bank_id" class="form-select"  onChange="selectBank();">
                        
                      </select>
                    </div>
                    
                  </div>
                   <div class="mb-3">
                       <fieldset class="form-fieldset">
                            <div class="mb-3">
                              <div class="form-label" id="skyhxx"></div>
                            </div>
                          </fieldset>
                   </div>
                  
                  <div class="mb-3">
                    <label class="form-label required"  th:text="#{deposit.depositamount}">Deposit Amount</label>
                    <div>
                      <input type="hidden" name="currency2" id="currency2" />
                      <input type="hidden" name="depositType" id="depositType" />
                      <input type="text" class="form-control" id="depositAmount" name="depositAmount" style="border: 1px solid #dee2e6;">
                    </div>
                    <small class="form-text text-muted ml-3" id="depostiNote">Please keep sufficient funds in your bank account, otherwise it may result in a rejection or overdraft.</small>
                  </div>
                  
                  <div class="mb-3" id="bankcountry">
                    <label class="form-label " th:text="#{withdraw.bankcoutry}">Bank Country</label>
                  <div class="input-group input-group-flat">
                     <select class="form-select" name="bank_name" id="bank_name"  >
                                 <option  th:each="column,columnStat:${countries}"   th:value="${column.countryCodeThree}">[[${column.countryName}]]</option>
                      </select>
                              
                  </div>
                  </div>
                  
                   <div class="mb-3" id="digitaladdress">
                    <label class="form-label " >Digital Currency Address</label>
                  <div class="input-group input-group-flat">
                              <input type="text" class="form-control pe-0"  id="bank_address"  name="bank_address" >
                            </div>
                  </div>
                  
                  
                    <div class="mb-3" id="uploaddocument">
                    <label class="form-label " >Upload Documents</label>
                  <div class="input-group input-group-flat">
                             <div id="imgsss"><img  height="60px;"/></div>
                         <input type="hidden"  id="imageFront" name="imageFront" value=""  >
                         <input type="file" name="picFieldName" id="picFieldId" onchange="uploadPic(this);" style="display:none;"/>
                         <button type="button" class="btn btn-primary" onclick="toUpload();"    th:text="#{profile.upload}">upload</button>
                            </div>
                  </div>
                  
                  <div class="mb-3">
                        <label class="form-label"  th:text="#{deposit.notes}">Notes</label>
                        <textarea class="form-control"   name="remark" id="remark"
							          rows="5"></textarea>
                      </div>
                </div>
                <div class="card-footer text-end">
                  <button type="button" id="tjbutton" onclick="toDeposit();" class="btn btn-primary"  th:text="#{deposit.submit}">Submit</button>
                </div>
              </form>
					</div>
                  </div>
                </div>
              </div>
              
              
             <!-- right begin -->
              
              <div class="col-md-6 col-lg-3">
                <div class="row row-cards">
                  
                  <div class="col-12">
                    <div class="row row-cards">
              
                  <div >
                  <a href="deposit">
                    <div class="card card-sm">
                      <div class="card-body">
                        <div class="row align-items-center">
                          <div class="col-auto">
                           <span >
                             <img src="../dist/deposit.png" height="100px;">
                            </span>
                          </div>
                          <div class="col">
                            <div class="font-weight-medium">
                             <h3  th:text="#{gobal.deposit}"> Deposit</h3>
                            </div>
                            <div class="text-muted"  th:text="#{gobal.prompt2}">
                              Deposit your funds<br/>
							  into your account
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    </a>
                  </div>
                  
                  
                   <div th:if="${userInfo.isAvailable == 1}">
                  <a href="withdraw">
                    <div class="card card-sm">
                      <div class="card-body">
                        <div class="row align-items-center">
                          <div class="col-auto">
                           <span >
                             <img src="../dist/withdraw.png" height="100px;">
                            </span>
                          </div>
                          <div class="col">
                            <div class="font-weight-medium">
                             <h3  th:text="#{gobal.withdraw}"> Withdraw</h3>
                            </div>
                            <div class="text-muted"  th:text="#{gobal.prompt3}">
                              Withdraw your<br/>
							  funds
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    </a>
                  </div>
                  
                <div th:if="${userInfo.isAvailable != 1}">
                  <a href="javascript:toAlert();">
                    <div class="card card-sm">
                      <div class="card-body">
                        <div class="row align-items-center">
                          <div class="col-auto">
                           <span >
                             <img src="../dist/withdraw.png" height="100px;">
                            </span>
                          </div>
                          <div class="col">
                            <div class="font-weight-medium">
                             <h3  th:text="#{gobal.withdraw}"> Withdraw</h3>
                            </div>
                            <div class="text-muted"  th:text="#{gobal.prompt3}">
                              Withdraw your<br/>
							  funds
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    </a>
                  </div>
                  
                  
                  
                      <div>
                   <a href="transfer">
                    <div class="card card-sm">
                      <div class="card-body">
                        <div class="row align-items-center">
                          <div class="col-auto">
                            <span >
                             <img src="../dist/transfer.png" height="100px;">
                            </span>
                          </div>
                          <div class="col">
                             <div class="font-weight-medium">
                              <h3  th:text="#{gobal.internaltransfer}">Internal transfer</h3>
                            </div>
                            <div class="text-muted"  th:text="#{gobal.prompt1}">
                              Transfer your funds<br/>
							  between acoounts

                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    </a>
                  </div>
                 
                </div>
                  </div>
                  
                </div>
              </div>
              
                <!-- right end -->
              
              
              
              
              

             
            </div>
          </div>
        </div>
        <!-- body end -->
        
         <form id="depositForm" method="POST">
            <input type="hidden" name="first_name" id="first_name" th:value="${userInfo.name}"/>
             <input type="hidden" name="last_name" id="last_name" th:value="${userInfo.surname}"/>
              <input type="hidden" name="email" id="email" th:value="${userInfo.userName}"/>
               <input type="hidden" name="postal_code" id="postal_code" th:value="${userInfo.country}"/>
                <input type="hidden" name="city" id="city" th:value="${userInfo.city}"/>
                 <input type="hidden" name="address" id="address" th:value="${userInfo.adress}"/>
                  <input type="hidden" name="currency" id="currency" value=""/>
                  <input type="hidden" name="login_id" id="login_id" value=""/>
                    <input type="hidden" name="notification_url" id="notification_url" value="https://seventy2u.seventybrokers.com/trade/payResult"/>
                       <input type="hidden" name="return_url" id="return_url" value="https://seventy2u.seventybrokers.com/trade/fundTransfer"/>
                       
                         <input type="hidden" name="amount" id="amount" value=""/>
                         <input type="hidden" name="serial_number" id="serial_number" value=""/>
                         <input type="hidden" name="sign" id="sign" value=""/>
                         
                         
                          <input type="hidden" name="KYC_status" id="KYC_status" th:value="${userInfo.isAvailable}"/>
                           <input type="hidden" name="ID_No" id="ID_No" th:value="${userInfo.identityNum}"/>
                         
         </form>
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
				<center>
                  <li class="list-inline-item">
                   ©Copyright 2025  All Rights Reserved. v[[${CRM_VERSION}]] - Licensed to [[${CRM_LICENSED}]]
                  </li>
				  </center>
                 
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
   <br/><br/>
     <div class="lest">
          <div class="conter">
            <div class="second" th:if="${isMobile==0}">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">Desktop MT5</div>
              <div class="button" style="margin-top:5px;"><img class="winimg" src="../img/win.png" alt=""><a th:href="${APP_URL1}">Windows</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a th:href="${APP_URL2}">MacOS</a></div>
            </div>
            <div class="second">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">Mobile MT5</div>
              <div class="button" style="margin-top:5px;"><img src="../img/google.png" alt=""><a  th:href="${APP_URL3}"  >Google</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a  th:href="${APP_URL4}">IOS</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/apk.png" alt=""><a  th:href="${APP_URL5}">APK</a></div>
            </div>
            <div class="second" th:if="${APP3_STATUS eq '1'}">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">70 Knows Mobile</div>
              <div class="button"   style="margin-top:5px;"><img src="../img/google.png" alt=""><a  th:href="${APP_URL6}" >Google</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a  th:href="${APP_URL7}">IOS</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/apk.png" alt=""><a  th:href="${APP_URL8}">APK</a></div>
            </div>
          </div>
        </div>
          <form class="card card-md" id="languageForm"  method="post">
                <input type="hidden" name="planguage" id="planguage"/>
        </form>
    <!-- Libs JS -->
    <script src="../dist/libs/apexcharts/dist/apexcharts.min.js?**********" defer></script>
    <script src="../dist/libs/jsvectormap/dist/js/jsvectormap.min.js?**********" defer></script>
    <script src="../dist/libs/jsvectormap/dist/maps/world.js?**********" defer></script>
    <script src="../dist/libs/jsvectormap/dist/maps/world-merc.js?**********" defer></script>
    <!-- Tabler Core -->
    <script src="../dist/js/tabler.min.js?**********" defer></script>
    <script src="../dist/js/demo.min.js?**********" defer></script>
        <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-4.4.4.min.js"></script>
    <script th:inline="javascript">
    
    var depositType=1;
    
    var dqye=0;
    var djje=0;
    
    var minrj=100;
    var maxrj=10000;
              
           function selectBank(){
            	  
            	  $.ajax({
        				//几个参数需要注意一下
        				type: "POST",//方法类型
        				url: "getBanInfo?bank_id="+$("#bank_id").val() ,//url
        				success: function (result) {
        						if (result!="false") {
        							var json = eval('(' + result + ')');
        							
        							
        						   depositType=json.currencyType;
        						   document.getElementById("depositType").value=json.currencyType;
        						   
        						   if(json.currencyType=="2"){
        							   document.getElementById("skyhxx").innerHTML="Accepted Currency:"+json.bankAccount
        							   +"<br/>"+"Beneficiary Name:"+json.backup16
        							   +"<br/>"+"Account Number:"+json.backup15
        							   +"<br/>"+"Bank Name:"+json.backup12
        							   +"<br/>"+"Bank Address:"+json.backup13
        							   +"<br/>"+"Swift Code:"+json.backup14;
        							   
        							   $("#bankcountry").show();
        							   $("#uploaddocument").show();
        							   $("#digitaladdress").hide();
        						   }else if(json.currencyType=="3"){
        							   document.getElementById("skyhxx").innerHTML="Accepted Currency:"+json.bankAccount
        							   +"<br/>"+"Digital Currency Address:"+json.backup17;
        							   $("#digitaladdress").show();
        							   $("#uploaddocument").show();
        							   $("#bankcountry").hide();
        						   }else{
        							   
        							   document.getElementById("skyhxx").innerHTML="Accepted Currency:"+json.bankAccount;
        							   $("#bankcountry").hide();
        							   $("#digitaladdress").hide();
        							   $("#uploaddocument").hide();
        						   }
        						   
        						   if(json.tel!=""&&json.tel!=undefined){
        							   minrj=json.tel*1;
        						   }
 									if(json.swift!=""&&json.swift!=undefined){
 										maxrj=json.swift*1;
        						   }
 									document.getElementById("currency2").value=json.bankAccount;
 									document.getElementById("currency").value=json.bankAccount;
 									document.getElementById("depositForm").action=json.accountName;
 									
 									
 								   if([[${userInfo.backup1}]]=="en"){
 									  document.getElementById("depostiNote").innerHTML=json.backup4
 								   }
 								  if([[${userInfo.backup1}]]=="ms"){
 									  document.getElementById("depostiNote").innerHTML=json.backup8
								   }
 								 if([[${userInfo.backup1}]]=="th"){
 									 document.getElementById("depostiNote").innerHTML=json.backup7
								   }
 								 if([[${userInfo.backup1}]]=="vi"){
 									 document.getElementById("depostiNote").innerHTML=json.backup9
								   }
 								 if([[${userInfo.backup1}]]=="id"){
 									 document.getElementById("depostiNote").innerHTML=json.backup10
								   }
 								 if([[${userInfo.backup1}]]=="zh-cn"){
 									 document.getElementById("depostiNote").innerHTML=json.backup5
								   }
 								 if([[${userInfo.backup1}]]=="zh-tw"){
 									 document.getElementById("depostiNote").innerHTML=json.backup6
								   }
        						}else{
        						}
        				},
        				error : function() {
        				}
        			});
            	  
              }
           
           function selectAccount(){
         	  
         	  $.ajax({
     				//几个参数需要注意一下
     				type: "POST",//方法类型
     				url: "getBankListByLoginId?loginid="+$("#trade_id").val() ,//url
     				success: function (result) {
     					 $("#bank_id").html("");
     						if (result!="false") {
     							var json = eval('(' + result + ')');
     							for(var m=0;m<json.length;m++){
     								var optionMonthCache='<option value="'+json[m].id+'">'+json[m].bankName+'</option>';
     				                $("#bank_id").append(optionMonthCache);
     							}
     							  selectBank();
     							  
     							  
     						}else{
     						}
     				},
     				error : function() {
     				}
     			});
         	  
         	 
         	  
           }
              
             
              function toDeposit(){
            	    if(document.getElementById("depositAmount").value==""){
            	    	layer.open({
            				style : 'margin-bottom : 70%;',
            				content : '[[#{deposit.prompt1}]]',
            				skin : 'msg',
            				time : 5000,btn:['OK']
             		   ,title: 'INFO'
            			});
            	    	return false;
            	    }
            	    
            	    if(!isNumber(document.getElementById("depositAmount").value)){
            	    	layer.open({
            				style : 'margin-bottom : 70%;',
            				content : '[[#{deposit.prompt2}]]',
            				skin : 'msg',
            				time : 5000,btn:['OK']
             		   ,title: 'INFO'
            			});
            	    	return false;
            	    }
            	    
            	    if(document.getElementById("depositAmount").value*1<minrj*1){
            	    	layer.open({
            				style : 'margin-bottom : 70%;',
            				content : '[[#{deposit.prompt3}]]:   '+minrj+'  USD',
            				skin : 'msg',
            				time : 5000,btn:['OK']
             		   ,title: 'INFO'
            			});
            	    	return false;
            	    }
            	    
            	    var maxDeposit=maxrj*1;
            	    
            	    if(document.getElementById("depositAmount").value*1>maxDeposit){
            	    	layer.open({
            				style : 'margin-bottom : 70%;',
            				content : '[[#{deposit.prompt4}]]:    '+maxDeposit+'  USD',
            				skin : 'msg',
            				time : 5000,btn:['OK']
             		   ,title: 'INFO'
            			});
            	    	return false;
            	    }
            	    
            	    $("#tjbutton").attr('disabled',true);
            	   
            	    $.ajax({
        				//几个参数需要注意一下
        				type: "POST",//方法类型
        				url: "saveDepositInfo.do" ,//url
        				data: $('#logonForm').serialize(),
        				success: function (result) {
        					if("false"==result) {
        						layer.open({
            						style : 'margin-bottom : 70%;',
            						content : 'Failed',
            						skin : 'msg',
            						time : 5000,btn:['OK']
            			    		   ,title: 'INFO',
            						end:function(){
            							$("#tjbutton").attr('disabled',false);
            						}
            					});
        						
        					}else{
        						
        						if(depositType=="1"){
        							var json22 = eval('(' + result + ')');
        							document.getElementById("amount").value=json22.amount;
        							document.getElementById("serial_number").value=json22.serial_number;
        							document.getElementById("sign").value=json22.sign;
        							document.getElementById("login_id").value=json22.login_id;
        							document.getElementById("depositForm").submit();
        						}else{
        							  layer.open({
        									style : 'margin-bottom : 70%;',
        									content : '[[#{ext.prompt15}]]',
        									skin : 'msg',
        									time : 5000,btn:['OK']
        						    		   ,title: 'INFO',
        									end:function(){
        										location.href="fundTransfer";
        									}
        								});
        						}
        							
        						}
        				
        				},
        				error : function() {
        					layer.open({
        						style : 'margin-bottom : 70%;',
        						content : 'Error',
        						skin : 'msg',
        						time : 5000,btn:['OK']
        			    		   ,title: 'INFO',
        						end:function(){
        							$("#tjbutton").attr('disabled',false);
        						}
        					});
        				}
        			});
              }
              
              
          
              selectAccount();
             
              
              function isNumber(val){
            	    var regPos = /^\d+(\.\d+)?$/; //非负浮点数
            	    var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
            	    if(regPos.test(val) || regNeg.test(val)){
            	        return true;
            	    }else{
            	        return false;
            	    }
            	}
               
    </script>
    
     <script>
     var client = new OSS.Wrapper({
         region : 'oss-ap-southeast-1',
         accessKeyId : 'LTAI4FwEEkfVCT3DnY1jh6cY',
         accessKeySecret : '******************************',
         secure: true,
         bucket : '70b'
     });
       
       function uploadPic(obj){
           var file=obj.files[0];//获取文件流
           var val= obj.value;
           var suffix = val.substr(val.indexOf("."));
           var storeAs = "everwings-oss/"+timestamp()+suffix;
           client.multipartUpload(storeAs, file).then(function (result) {
           	
           	if(result.res.requestUrls[0].indexOf('?') != -1){
                   this.url = result.res.requestUrls[0].split('?')[0];
                   document.getElementById("imageFront").value=url;
                   document.getElementById("imgsss").innerHTML="<img src='"+url+"' height='60px;'/>";
               }else{
                   //console.log('图片100k以内')
                   this.url = result.res.requestUrls[0];
                   document.getElementById("imageFront").value=url;
                   document.getElementById("imgsss").innerHTML="<img src='"+url+"' height='60px;'/>";
               }
           	
        
           }).catch(function (err) {
           	
           	alert(err);
              
           });
       }
       
       

 
    /**
     * 生成文件名
     * @returns
     */
    function timestamp(){
        var time = new Date();
        var y = time.getFullYear();
        var m = time.getMonth()+1;
        var d = time.getDate();
        var h = time.getHours();
        var mm = time.getMinutes();
        var s = time.getSeconds();
        return ""+y+add0(m)+add0(d)+add0(h)+add0(mm)+add0(s)+add0(time.getTime());
    }
    function add0(m){
        return m<10?'0'+m : m;
    }
    
    function toUpload(){
    	　document.getElementById("picFieldId").click(); 
    }
  
    </script>
    	<script  th:inline="javascript">

function updateaLang(obj){

	    document.getElementById("planguage").value=obj;
		$.ajax({
			//几个参数需要注意一下
			type: "POST",//方法类型
			url: "saveLanguage.do" ,//url
			data: $('#languageForm').serialize(),
			success: function (result) {
				
				location.reload();
			},
			error : function() {
			
			}
		});
	
	
}
function toAlert(){
	layer.open({
		style : 'margin-bottom : 70%;',
		content : '[[#{ext.prompt1}]]',
		skin : 'msg',
		time : 5000,btn:['OK']
	   ,title: 'INFO'
	});
	
};

document.getElementById("trade_id").value=[[${param.id}]];
</script>	
<script src="https://cdn.customgpt.ai/js/chat.js"  th:inline="javascript" th:if="${FOOTER2_SCRIPT_STATUS} eq '1'"></script>
<script th:if="${FOOTER2_SCRIPT_STATUS} eq '1'" th:inline="javascript" >window.onload = function () { CustomGPT.init({p_id: [[${JS2_ID}]], p_key: [[${JS2_KEY}]], reset_conversation: "1"}); };</script>
 
<!-- Start of seventyinvestech Zendesk Widget script -->
<script id="ze-snippet" th:src="@{'https://static.zdassets.com/ekr/snippet.js?key='+${JS_KEY}}"  th:if="${FOOTER_SCRIPT_STATUS} eq '1'"> </script >
<!-- End of seventyinvestech Zendesk Widget script -->
  </body>
</html>