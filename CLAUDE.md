# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Spring Boot CRM system** for MT4 trading platform management. The application provides customer relationship management functionality for forex trading operations including user management, trade accounts, fund transfers, deposits/withdrawals, and system configuration.

## Technology Stack

- **Backend**: Spring Boot 2.1.1, Java 8
- **Database**: MySQL 5.x with JPA/Hibernate
- **Security**: Apache Shiro 1.3.2, JWT authentication
- **Frontend**: Thymeleaf templates, Bootstrap, jQuery
- **Build**: Maven
- **Server**: Embedded Tomcat (WAR deployment)

## Architecture

The codebase follows a standard Spring Boot layered architecture:

```
com.ews/
├── NewCrmApplication.java          # Main application class
├── crm/                            # CRM business logic
│   ├── controller/                 # REST endpoints & web controllers
│   ├── service/                    # Business logic services
│   ├── repository/                 # JPA repositories
│   └── entity/                     # JPA entities
├── system/                         # System management
│   ├── controller/                 # User/role/permission management
│   ├── service/                    # System services
│   └── entity/                     # System entities
├── wx/                             # WeChat integration
├── config/                         # Application configuration
│   ├── shiro/                      # Security configuration
│   ├── jwt/                        # JWT token handling
│   └── filter/                     # Custom filters
└── common/                         # Shared utilities
    ├── websocket clients/          # MT4 WebSocket integrations
    └── utilities/                  # Common functions
```

## Key Business Domains

### CRM Module (`com.ews.crm`)
- **TradeAccount**: Trading account management
- **UserInfo**: Customer information management  
- **FundInfo**: Fund/investment product management
- **OrderInfo**: Trading orders and transactions
- **TransferInfo**: Internal fund transfers
- **Deposit/Withdrawal**: Payment processing systems

### System Module (`com.ews.system`)
- **User/Role/Permission**: RBAC authorization system
- **Enclosure**: File upload management
- **WebContent**: CMS for web pages

### WeChat Integration (`com.wx`)
- WeChat message handling
- OAuth2 integration
- Menu management

## Development Commands

### Build and Run
```bash
# Build the project
mvn clean compile

# Build WAR file
mvn clean package

# Run locally (dev profile)
mvn spring-boot:run -Dspring.profiles.active=dev

# Run with production profile
mvn spring-boot:run -Dspring.profiles.active=prod
```

### Database Configuration
- **Dev**: `application-dev.yml` - Local MySQL on port 3306
- **Prod**: `application-prod.yml` - Production MySQL settings
- **Schema**: `crm_mt4new` database

### Configuration Files
- `application.yml` - Active profile configuration
- `application-dev.yml` - Development settings
- `application-prod.yml` - Production settings
- `logback-spring.xml` - Logging configuration

## Key Configuration Points

### Database Connection
```yaml
spring:
  datasource:
    url: *************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.jdbc.Driver
```

### File Storage
- **Development**: `F:\work\crm_prod\...\static\fileserver\`
- **Production**: `D:\\tomcat-9\webapps\ROOT\...\fileserver\`

### WebSocket Integration
The system uses WebSocket clients for real-time MT4 data:
- `MyWebsocketClient4Balance` - Account balance updates
- `MyWebsocketClient4TradeAccount` - Trading account info
- `MyWebsocketClient4FundInfo` - Fund information
- `MyWebsocketClient4History` - Trade history

## Security Features

### Authentication Flow
1. **JWT Token**: Stateless authentication with JWT
2. **Shiro Integration**: Role-based access control
3. **CORS Configuration**: Cross-origin request handling
4. **Session Management**: Configurable session timeouts

### User Roles
- **Admin**: Full system access
- **Manager**: Department-level access
- **User**: Customer-level access

## Testing Strategy

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=UserServiceTest

# Run with coverage
mvn jacoco:report
```

## Deployment

### Local Development
```bash
# Start MySQL service
# Ensure database 'crm_mt4new' exists
mvn spring-boot:run -Dspring.profiles.active=dev
```

### Production Deployment
1. Build WAR: `mvn clean package`
2. Deploy to Tomcat: Copy `target/newcrm-0.0.2-SNAPSHOT.war` to Tomcat webapps
3. Configure production database in `application-prod.yml`

## Common Development Tasks

### Adding New Entity
1. Create entity in `com.ews.crm.entity`
2. Create repository in `com.ews.crm.repository`
3. Create service interface and implementation
4. Create controller in `com.ews.crm.controller`
5. Add appropriate permissions in Shiro configuration

### Database Changes
1. Update entity classes
2. Use Spring Data JPA for migrations
3. Test with both dev and prod configurations

### Frontend Templates
- Located in `src/main/resources/templates/`
- Uses Thymeleaf templating engine
- Static resources in `src/main/resources/static/`

## Environment-Specific Notes

### Development Environment
- Port: 8099
- Debug mode enabled
- Hot reload with devtools
- Local file storage paths

### Production Environment  
- Port: 80 (default HTTP)
- Debug logging disabled
- Production file paths
- Optimized for performance

## Troubleshooting

### Common Issues
- **Database connection**: Check MySQL service and credentials
- **File upload**: Verify file storage paths exist and have write permissions
- **WebSocket connection**: Ensure MT4 server connectivity
- **Port conflicts**: Change port in application-{profile}.yml

### Debug Logging
Enable debug logging by adding to application-dev.yml:
```yaml
logging:
  level:
    com.ews: DEBUG
    org.springframework.web: DEBUG
```